@font-face {
    font-family: "Elza";
    src: url(../fonts/Elza/Elza.otf);
    font-weight: 400;
  }

@font-face {
font-family: "Elza";
src: url(../fonts/Elza/Elza_Condensed_Extralight.otf);
font-weight: 300;
}

@font-face {
    font-family: "Elza";
    src: url(../fonts/Elza/Elza_Condensed_Medium.otf);
    font-weight: 500;
}

a:hover{color:rgb(var(--bs-warning-rgb));}
.paragraph{
    line-height: 1.5rem;
    font-weight: 300;
    font-size: 1.1rem;
}
 
.concrete {background: url('../images/concrete.jpg'); background-size: cover ;}
:root{
    --bs-warning-rgb: 72,150,62;
    --bs-primary-rgb: 36,35,49;
    --bs-secondary-rgb: 32,32,45;
    --bs-dark: 26,25,37;
    --bs-light: 255,255,255;
    --bs-info: 221,221,241;
    --bs-red: 209,32,18;
    --bs-body-font-family: "Elza",sans-serif;
}

.fw-bold {
  font-weight: 500!important;
  }
  .bg-red{background-color: rgb(var(--bs-red)) !important}  
  .bg-primary{background-color: white !important} 
.text-red{color: rgb(var(--bs-red))}
#submit--button{
    font-family: "Elza",sans-serif;
    background-color: rgb(var(--bs-red));
    font-size: 1.2rem;
    text-transform: uppercase;
    padding: 0.5rem 2rem 0.5rem;
    border-radius: 0.3rem;
    border: none;
    color: white;
    width: auto;
    line-height: 1.2rem;
}

.input--field{
    font-size: 0.8rem;
    padding: 0.6rem 1rem;
    border-radius: 0.3rem;
    border: none;
    margin: 1.1rem;
}


h1, h2, h3, h4, h5, h6, .fontt {
    font-family:"Elza",sans-serif;
}

.navbar-dark .navbar-nav .nav-link{
    color: white;
    font-size: 1rem;
}
.navbar-dark .navbar-toggler{border-color: rgb(var(--bs-red));}
.navbar-dark .navbar-toggler-icon{filter: invert();}
a{
    color:inherit;
    text-decoration: none;
}
td{min-width:30%; max-width: 50%;}
.border--color--red{
    border-color: var(--bs-warning-rgb);
}
.modal{z-index: 999999!important;}
.modal-dialog{max-width:800px !important}
.carousel-indicators-hero{
    display: flex;
    align-items: center;
    position: relative;
    width: 5rem;
    margin-left:8%;
    margin-top: -10%;
    margin-right:0;
    justify-content: space-around;
}
.certificates{max-width:70%;filter:grayscale(0);opacity:1;transition: all 500ms;}
.certificates:hover{filter:grayscale(1);opacity:0.5}
.recenzie{
    margin-top: -7rem;
    position: absolute;
    bottom: -24px;
    display: flex;
    align-items: center;
}

.recenzie [data-bs-target] {
    width: 3rem;
    height: 3px;
    opacity: 1;
}

.recenzie div{
    position: absolute;
}

.recenzie .active{
    height: 7px;
}

.recenzia--text{

    font-weight: 400;
    padding-bottom: 3rem;
    min-height:130px;
}
.uvodzovky::before{filter:invert(); content: "";display: inline-block; width:40px;height: 40px; margin:0 20px 0 10px;background: url('../images/icons/uvodzovky.svg');background-repeat: no-repeat;}
.uvodzovky::after{filter:invert();content: "";display: inline-block; width:40px;height: 40px; margin:0 10px 0 20px;background: url('../images/icons/uvodzovky.svg');background-repeat: no-repeat;transform:rotate(180deg)}


.carousel-indicators-hero [data-bs-target]{
    width: 0.5rem;
    height: 0.5rem;
    padding:0;
    margin: 0.25rem;
    border-radius: 9rem;
    background-color: white;
    border: none;
    opacity: 1;
}
#map{height: 500px;}
.grid-item { width: 20%; }
/* 2 columns */
.grid-item--width2 { width: 40%; }
.grid-item img{max-width: 100%;}

.hovereffect:hover {
 
    background-color: #232331;
}

.image {
  opacity: 1;
  display: block;
  width: 100%;
  height: auto;
  transition: .5s ease;
  backface-visibility: hidden;
}

.overlay {
  transition: .5s ease;
  opacity: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  text-align: center;
}

.hovereffect:hover .image {
  opacity: 0.3;

}

.hovereffect .overlay {
    max-width: 40px;
  
  }
.hovereffect:hover .overlay {
  opacity: 1;

}

.overlay a {
  background-color: transparent;

  font-size: 16px;
  padding: 16px 32px;
}
.fancybox-overlay{z-index: 100001 !important;}


.max-h-5\.5{
    max-height: 5.5rem;
}

.carousel-indicators-hero .active{

    margin: 0;
    border: 0.25rem solid rgb(100,56,60);
    background-color: rgb(var(--bs-red));
}

.carousel-caption{
    bottom: 30%;
    display:flex;
    text-align: left;
    left: 8%;

}

#carouselExampleCaptions{
    display: flex;
    flex-direction: column;
}

.navbar > *{
    height: 100%;
}

.navbar{
    z-index: 100000;
    padding: 0;
    overflow-y: visible;
    font-size: 1.5rem;
    text-transform: uppercase;
    position:fixed;
    background-color: white;
    width: 100%;
    border-top:2px solid rgb(var(--bs-red));
    border-bottom: 1px solid rgb(83,79,95);
    top:0;
}

#nav--logo{

    overflow:visible;
    display: flex;
    justify-content: center;
}

#nav--logo img{max-width: 11.2rem;} 


.decoration{
  width: 0; 
  height: 0; 
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-top: 15px solid #49963e;
  rotate: 135deg;
  margin-left: -2.5rem;
}

.decoration2{
  width: 0; 
  height: 0; 
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-top: 15px solid #49963e;
  rotate: -135deg;
  margin-left: 0rem;
margin-top: -1rem;
}

.list--item{
    display: flex;
    margin-bottom: 1rem;
    margin-top: 1rem;

    
}

.list--item a{
    height: 100%;
    display: flex;
    align-items: center;

}

 nav ul{
    display: inline-flex;
    flex-direction: row;
    list-style: none;
    font-family: 'Poppins',sans-serif;
    font-weight: 200;
    align-content: center;
    height: 100%;
    margin-bottom: 0;
    margin-right: 1rem;
    margin-left: 1rem;
    
} 


#nav--tel{

    flex-direction: row;
    align-items: center;
    justify-content: center;
    border-left: 1px solid rgba(83,79,95, .3);
    border-right: 1px solid rgba(83,79,95, .3);
    
    display: none;
    font-family:"Elza",sans-serif;
    font-size: 1.2rem;
    height: 100%;
}

#navbarTogglerDemo03 .active{
    border-bottom:3px solid rgb(var(--bs-warning-rgb)) ;
    margin-bottom: -3px;
    padding-top: 3px;
}



#nav--tel a{
    text-decoration: none;
}

.hover-underline {
font-weight: 500;
    display: flex;
    position: relative;
    margin-bottom: -2px;
    text-decoration:none;

}


.hover-underline:after {
content: "";
position: absolute;
width: 100%;
transform: scaleX(0);
height: 3px;
bottom: 0;
left: 0;
background-color: rgb(var(--bs-warning-rgb));
transform-origin: bottom right;
transition: transform 0.25s ease-out;
}

.hover-underline:hover:after {
transform: scaleX(1);
transform-origin: bottom left;
}

.selectpicker{
    background-color: white;
    border: none;
    margin-left: 1rem;
    font-family: "Elza",sans-serif;
}

#nav--logo{
    height: 5rem;
    overflow:visible;
    display: flex;
    justify-content: flex-end;
}
  
  .custom-select select {
    display: none; /*hide original SELECT element: */
  }

  .custom-select2 select {
    display: none; /*hide original SELECT element: */
  }

  
  /* Style the arrow inside the select element: */
  .select-selected:after {
    position: absolute;
    content: "";
    display: inline-flex;
    margin:7px;

    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-color: rgb(var(--bs-red))transparent transparent transparent;
  }

  .custom-select2 .select-selected:after {
    position: absolute;
    content: "";
    display: inline-flex;
    margin:7px;

    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-color: #000 transparent transparent transparent;
  }
  
  /* Point the arrow upwards when the select box is open (active): */
  .select-selected.select-arrow-active:after {
    border-color: transparent transparent rgb(var(--bs-red)) transparent;
    margin-top: 1px;
  }

  .custom-select2 .select-selected.select-arrow-active:after {
    border-color: transparent transparent #000 transparent;
    margin-top: 1px;
  }
  
  /* style the items (options), including the selected item: */
  .select-items div,.select-selected {
    padding: 8px 16px 8px 0px;
    border: 1px solid transparent;
    border-color: transparent transparent rgba(f, f, f, 0.1) transparent;
    cursor: pointer;
  }

  .custom-select2 .select-items div,.select-selected {
 
    padding: 8px 16px 8px 16px;
    border: 1px solid transparent;
    border-color: transparent transparent rgba(f, f, f, 0.1) transparent;
    cursor: pointer;
  }

  .custom-select2 .select-items div, .custom-select2 .select-selected {
    color: #000;
  }
  
  /* Style items (options): */
  .select-items {
    position: absolute;
    background-color: white;

    z-index: 99;
    text-align: center;
    padding-left: 16px;
    margin-left: -16px;
    padding-right: 24px;
  }

  .custom-select2 .select-items {
    position: absolute;
    background-color: white;
    margin: 0 10rem;
    z-index: 99;
    text-align: center;
    margin: 0 auto;
    padding: 8px 16px;
    border-radius: 5px;
    width: 79vw;
  }
  @media (min-width:1200px){
.display-5 {
font-size: 2.5rem;
}}
  /* Hide the items when the select box is closed: */
  .select-hide {
    display: none;
  }
  
  .select-items div:hover, .same-as-selected {
    background-color: rgba(0, 0, 0, 0.1);
  }

  .custom-select2{
      background-color: white;
      color:black;
  }

.accordion-button:not(.collapsed)::after {
    background-image: url(../images/icons/-.png);
}

.accordion-button::after{
    background-image: url(../images/icons/+.png);
    margin-bottom: 1.2rem;

}

.accordion-button:not(.collapsed){
    background: none;
    color: black;
    box-shadow: none;
}

.accordion-item{
    border: none;
    
}

.accordion-button{
    padding: 0;
    background: none;
}

.accordion-body{
    padding: 0.5rem 1.25rem 2.5rem;
    line-height: 2rem;
    font-size: 1rem;
    font-weight: 300;
}
#contact_success{color: #00933e;margin-bottom: 20px;}
/*---social-icons-----*/
.social-media {
    margin-top:0px;
    float:right;
  }
  .social-media li{
    float:left;
    margin: 0px 1px 10px 0;
    display:inline-block;
  }
  .social-media li a {
    height: 45px;
    width: 45px;
    display: block;
    background: url(../images/img-sprite.png);
  }
  /*--------social simptip--------*/
  [data-tooltip] {
    position: relative;
    display: inline-block;
  }
  [data-tooltip].simptip-position-top:before {
    border-top-color: #323232;
  }
  [data-tooltip].simptip-position-top:after {
    background-color: #323232;
    color: #ecf0f1;
  }
  [data-tooltip]:before {
    content: '';
    position: absolute;
    border-width: 6px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0);
  }
  [data-tooltip]:before, [data-tooltip]:after {
    position: absolute;
    visibility: hidden;
    opacity: 0;
    z-index: 999999;
  }
  [data-tooltip]:after {
    height: 22px;
    padding: 10px 10px 0;
    font-size: 13px;
    line-height: 1px;
    content: attr(data-tooltip);
    white-space: nowrap;
  }
  [data-tooltip]:hover, [data-tooltip]:focus {
    background-color: rgba(0, 0, 0, 0);
  }
  [data-tooltip]:hover:before, [data-tooltip]:hover:after, [data-tooltip]:focus:before, [data-tooltip]:focus:after {
    visibility: visible;
    opacity: 1;
  }
  .simptip-position-top:before, .simptip-position-top:after {
    bottom: 120%;
  }
  .simptip-position-top.simptip-movable:before {
    margin-bottom: -5px;
  }
  .simptip-position-top.simptip-movable:after {
    margin-bottom: 7px;
  }
  .simptip-position-top:before, .simptip-position-top:after {
    left: 33%;
  }
  .simptip-position-top:after {
    margin-left: -28px;
  }
  .simptip-position-top.simptip-movable:before, .simptip-position-top.simptip-movable:after {
    -webkit-transition: all .1s linear;
    -moz-transition: all .1s linear;
    -o-transition: all .1s linear;
    -ms-transition: all .1s linear;
    transition: all .1s linear;
  }
  .simptip-position-top.simptip-movable:hover:before, .simptip-position-top.simptip-movable:hover:after {
    -webkit-transform: translateY(10px);
    -moz-transform: translateY(10px);
    -ms-transform: translateY(10px);
    -o-transform: translateY(10px);
    transform: translateY(10px);
  }
  /*------------end social simptip----------------*/
  .social-media li:nth-child(1) a{
    background: url(../images/img-sprite.png) -19px -170px;
  }
  .social-media li:nth-child(1):hover a{
    background: url(../images/img-sprite.png) -19px -123px;
  }
  .social-media li:nth-child(2) a{
    background: url(../images/img-sprite.png?v2) -65px -170px;
  }
  .social-media li:nth-child(2):hover a{
    background: url(../images/img-sprite.png?v2) -65px -123px;
  }
  .social-media li:nth-child(3) a{
    background: url(../images/img-sprite.png) -111px -170px;
  }
  .social-media li:nth-child(3):hover a{
    background: url(../images/img-sprite.png) -111px -123px;
  }
@media (min-width: 768px){
    #nav--logo{
        transition: height 0.3 ease-in-out;
        justify-content: center;
    }

    .margin--for--main--logo{
        margin-bottom: -5.5rem;
    }



    .custom-select2 .select-items {

        padding: 8px 68px;
        width: auto;
      }
 

}

@media (min-width: 992px){
    .navbar{
        font-size: 0.6rem;
    }
    #navbarTogglerDemo03{
        height: 5.5rem;
    }

    .list--item{
        margin-top: 0;
        margin-bottom: 0;
    }

    #nav--tel{
        display: flex;
    }

    .navbar{
        font-size: 0.8rem;
    }
}

@media (min-width: 1200px){
    #nuladva{
        margin-top: 60vh;
        position: absolute;
    }

    #nulajedna{
        margin-bottom: 25vh;
    }

    #nulatri{
        margin-top: 20vh;
    }
}

@media (min-width: 1400px){
    #nuladva{
        margin-top: 45vh;
    }

    #nulajedna{
        margin-bottom: 20vh;
    }
}

@media (max-width: 768px){
  .eco-icon{display: none;}
  #nav--logo img{max-width: 5.5rem;} 
    .grid-item { width: 50%; }
/* 2 columns */
.grid-item--width2 { width: 50%; }
}
 
  
}