<?php

    // Only process POST reqeusts.
    if ($_SERVER["REQUEST_METHOD"] == "POST") {
        // Get the form fields and remove whitespace.
        $name = strip_tags(trim($_POST["name"]));
		$name = str_replace(array("\r","\n"),array(" "," "),$name);
        $email = filter_var(trim($_POST["email"]), FILTER_SANITIZE_EMAIL);
        $phone = trim($_POST["phone"]);
        $amount = trim($_POST["amount"]);
        $location = trim($_POST["location"]);
        $cerpanie = trim($_POST["cerpanie"]);
        // Check that data was sent to the mailer.
        if ( empty($name) OR empty($phone) OR !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            // Set a 400 (bad request) response code and exit.
            http_response_code(400);
            echo "Nepodarilo sa odoslať formulár. Vyplňte všetky polia a skúste to znova.";
            exit;
        }

        // Set the recipient email address.
        // FIXME: Update this to your desired email address.
        $recipient = trim($_POST["emailto"]);

        // Set the email subject.
        $subject = "Novy dopyt od $name";

        // Build the email content.
        $email_content = "Meno: $name\r\n";
        $email_content .= "Email: $email\r\n";
        $email_content .= "Telefón: $phone\r\n";
        $email_content .= "Lokalita: $location\r\n";
        $email_content .= "Množstvo betónu: $amount\r\n";
        $email_content .= "Čerpanie: $cerpanie\r\n";

        // Build the email headers.
      $email_headers =  "MIME-Version: 1.0\r\nContent-Type: text/plain; charset=UTF-8\r\n";
      $email_headers .= "From: $name <$email>\r\n";


        // Send the email.
        if (mail($recipient, $subject, $email_content, $email_headers)) {
            echo "sent";
        } else {
            // Set a 500 (internal server error) response code.
           // http_response_code(500);
            echo "Nepodarilo sa odoslať Vašu správu.";
        }

    } else {
        // Not a POST request, set a 403 (forbidden) response code.
        http_response_code(403);
        echo "Nepodarilo sa odoslať formulár. Vyplňte všetky polia a skúste to znova.";
    }

?>