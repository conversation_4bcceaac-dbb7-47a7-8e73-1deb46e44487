jQuery(document).ready(function($) {
	$(".contacterror").hide();
	jQuery("#contact_me").submit(function() {
		
		var hasError = false;
		var emailReg = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  
		var emailVal = $("#contact_email").val();
		if(emailVal == '') {
			$(".email-error").show();
			$(".email-valid").hide();
			hasError = true;
		} else if(!emailReg.test(emailVal)) {
			$(".email-error").hide();
			$(".email-valid").show();
			hasError = true;
		}else{
			$(".email-error").hide();
			$(".email-valid").hide();
		}

		
		var nameVal = $("#contact_name").val();
		var phone = $("#phone").val();
		var amount = $("#amount").val();
		var location = $("#location").val();
		var cerpanie = $("#cerpanie").val();
		

		//alert(ajaxurl);
		if(hasError == false) {
			//$(this).hide();
			//$(".name-error").show();
			$("#sendEmail li.buttons").append('<img src="/wp-content/themes/default/images/template/loading.gif" alt="Loading" id="loading" />');
		
			var str = jQuery("#contact_form").serialize();
			//alert(str);
		
		
			data = { action: 'contact_form','name': nameVal, 'email': emailVal, 'phone': phone,'amount': amount,'location': location,'cerpanie': cerpanie,'emailto': $("#contact_emailto").val()};
			//jQuery.post(ajaxurl, data, function(response){
			jQuery.ajax({
				type: "POST",
				url: ajaxurl,
				data: data,
				success: function(response) {
					//alert(response);
					if(response == 'sent') {
						jQuery(".contact #node").hide();
						jQuery("#contact_success").fadeIn("slow");
						
						document.getElementById('contact_name').value = "";
						document.getElementById('contact_email').value = "";
						document.getElementById('phone').value = "";
						document.getElementById('location').value = "";
						document.getElementById('amount').value = "";
						document.getElementById('cerpanie').value = "";
					}
					else {
						result = msg;
						jQuery(".contact #node").html(result);
						jQuery(".contact #node").fadeIn("slow");
					}
				}
			});
		}
		return false;
	});
});